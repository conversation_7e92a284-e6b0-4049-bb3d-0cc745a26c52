<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chargebolt.hera</groupId>
    <artifactId>chargebolt-hera</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <modules>
        <module>hera-client</module>
        <module>hera-domain</module>
        <module>hera-dao</module>
        <module>hera-interceptor</module>
        <module>hera-web</module>
        <module>platform-common</module>
        <module>platform-wechat</module>
        <module>platform-wooshpay</module>
        <module>platform-pingpong</module>
        <module>platform-vietqr</module>
        <module>platform-zalopay</module>
        <module>platform-zalomini</module>
        <module>platform-antom</module>
        <module>platform-midtrans</module>
        <module>platform-airwallex</module>
        <module>platform-ifortepay</module>
        <module>platform-account</module>
    </modules>
    <packaging>pom</packaging>
    <name>hera</name>
    <description>支付平台</description>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <spring.version>5.2.15.RELEASE</spring.version>
        <springboot.version>2.3.12.RELEASE</springboot.version>
        <springcloud.version>2.2.9.RELEASE</springcloud.version>
        <jackson.version>2.11.4</jackson.version>

        <hera.client.version>1.0.25-SNAPSHOT</hera.client.version>
        <hera.domain.version>1.0.0-SNAPSHOT</hera.domain.version>
        <hera.dao.version>1.0.0-SNAPSHOT</hera.dao.version>
        <hera.interceptor.version>1.0.0-SNAPSHOT</hera.interceptor.version>
        <platform.common.version>1.0.0-SNAPSHOT</platform.common.version>
        <platform.wechat.version>1.0.0-SNAPSHOT</platform.wechat.version>
        <platform.wooshpay.version>1.0.0-SNAPSHOT</platform.wooshpay.version>
        <platform.pingpong.version>1.0.0-SNAPSHOT</platform.pingpong.version>
        <platform.vietqr.version>1.0.0-SNAPSHOT</platform.vietqr.version>
        <platform.zalopay.version>1.0.0-SNAPSHOT</platform.zalopay.version>
        <platform.zalomini.version>1.0.0-SNAPSHOT</platform.zalomini.version>
        <platform.antom.version>1.0.0-SNAPSHOT</platform.antom.version>
        <platform.midtrans.version>1.0.0-SNAPSHOT</platform.midtrans.version>
        <platform.airwallex.version>1.0.0-SNAPSHOT</platform.airwallex.version>
        <platform.ifortepay.version>1.0.0-SNAPSHOT</platform.ifortepay.version>
        <platform.account.version>1.0.0-SNAPSHOT</platform.account.version>
        <swagger.version>2.9.2</swagger.version>
        <rocketmq.version>4.7.1</rocketmq.version>
        <mofa.version>1.0.24-SNAPSHOT</mofa.version>
        <druid.version>1.1.10</druid.version>
        <mybatis.version>2.1.1</mybatis.version>
        <mysql.version>8.3.0</mysql.version>
        <lombok.version>1.18.16</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.chargebolt</groupId>
                <artifactId>commons-eden</artifactId>
                <version>2.5.5-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.chargebolt</groupId>
                <artifactId>hera-client</artifactId>
                <version>${hera.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chargebolt.hera</groupId>
                <artifactId>hera-domain</artifactId>
                <version>${hera.domain.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chargebolt.hera</groupId>
                <artifactId>hera-dao</artifactId>
                <version>${hera.dao.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chargebolt.hera</groupId>
                <artifactId>hera-interceptor</artifactId>
                <version>${hera.interceptor.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chargebolt.hera</groupId>
                <artifactId>platform-common</artifactId>
                <version>${platform.common.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>22.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>so.dian.center</groupId>
                <artifactId>dian-common</artifactId>
                <version>1.0.13-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.11</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>so.dian.mofa3</groupId>
                <artifactId>common-client</artifactId>
                <version>${mofa.version}</version>
            </dependency>
            <dependency>
                <groupId>so.dian.mofa3</groupId>
                <artifactId>common-template</artifactId>
                <version>${mofa.version}</version>
            </dependency>
            <!-- test -->
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>6.14.3</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>so.dian.mofa3</groupId>
                <artifactId>common-lang</artifactId>
                <version>${mofa.version}</version>
            </dependency>
            <dependency>
                <groupId>so.dian.mofa3</groupId>
                <artifactId>common-client</artifactId>
                <version>${mofa.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>