package so.dian.hera.service.v2.enums;

/**
 * 退款来源类型枚举
 * 用于标识退款的资金来源
 * 
 * <AUTHOR>
 */
public enum RefundSourceType {
    
    /** 预授权退款 */
    PRE_AUTH("预授权退款"),
    
    /** 押金抵扣退款 */
    DEPOSIT_DEDUCTION("押金抵扣退款"),
    
    /** 直接支付退款 */
    DIRECT_PAYMENT("直接支付退款");
    
    private final String description;
    
    RefundSourceType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
