package so.dian.hera.service.v2;

import static so.dian.platform.common.constants.SystemConstants.REFUND_SERVICE_NAME;

import com.chargebolt.hera.client.dto.param.base.ChannelRetMsg;
import com.chargebolt.hera.client.dto.param.rq.RefundOrderRQ;
import com.chargebolt.hera.client.dto.param.rs.UnifiedRefundRS;
import com.chargebolt.hera.client.dto.pay.refund.v2.RefundRQ;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.PaymentDO;
import com.chargebolt.hera.domain.RefundDO;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.hera.dao.rds.hera.PaymentRecordMapper;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.interceptor.v2.IRefundService;
import so.dian.hera.interceptor.v2.model.PayChannelConfigContext;
import so.dian.mofa3.lang.domain.Result;
import com.chargebolt.hera.client.enums.account.AccountTypeEnum;
import so.dian.platform.account.dao.UserAccountDAO;
import so.dian.platform.account.domain.UserAccountDO;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.utils.ValidateUtil;

@Service
public class UnifiedRefundService {
    @Resource
    private PaymentRecordMapper paymentRecordMapper;
    @Resource
    private ChannelConfigService configContextService;
    @Resource
    private BeanContextHelper beanContextHelper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private UserAccountDAO userAccountDAO;

    /**
     * 统一退款入口，基于支付溯源逻辑进行处理
     *
     * @param refundRQ 退款请求
     * @return 退款结果
     */
    public Result<UnifiedRefundRS> refund(RefundRQ refundRQ) {
        // 1. 查找目标支付单
        PaymentDO targetPayment = findTargetPayment(refundRQ);
        ValidateUtil.requiredNotNull(targetPayment, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 2. 构建退款上下文（核心溯源逻辑）
        PaymentRefundContext refundContext = buildRefundContext(targetPayment, refundRQ);

        // 3. 执行退款
        return executeRefund(refundContext);
    }

    /**
     * 处理订单退款
     *
     * @param refundRQ 订单退款请求
     * @return 退款结果
     */
    private Result<UnifiedRefundRS> processOrderRefund(RefundRQ refundRQ) {
        // 1. 查找支付单
        PaymentDO payment = findPaymentForOrderRefund(refundRQ);
        ValidateUtil.requiredNotNull(payment, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 2. 校验可退款金额
        validateRefundableAmount(refundRQ, payment);

        // 3. 执行单笔退款
        UnifiedRefundRS rs = executeSingleRefund(payment, refundRQ.getAmount(), refundRQ);
        return Result.success(rs);
    }

    /**
     * 处理押金退款
     *
     * @param refundRQ 押金退款请求
     * @return 退款结果
     */
    private Result<UnifiedRefundRS> processDepositRefund(RefundRQ refundRQ) {
        // 1. 查询用户押金账户
        UserAccountDO userAccount = userAccountDAO.selectUserAccountByParams(refundRQ.getUserId(),
                refundRQ.getTenantId(),
                AccountTypeEnum.DEPOSIT.getCode());
        ValidateUtil.requiredNotNull(userAccount, HeraBizErrorCodeEnum.ACCOUNT_NOT_EXIST);

        // 2. 校验退款金额是否超过押金余额
        if (refundRQ.getAmount() > userAccount.getAvailableAmount()) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ACCOUNT_BALANCE_NOT_ENOUGH);
        }

        // 3. 根据押金账户记录的 tradeNo 找到对应的原始支付单
        String depositPayTradeNo = userAccount.getTradeNo();
        if (StringUtils.isEmpty(depositPayTradeNo)) {
            throw HeraBizException.create(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT, "押金账户未关联支付单，无法退款");
        }
        PaymentDO payment = paymentRecordMapper.getPaymentByPayTradeNo(depositPayTradeNo);
        ValidateUtil.requiredNotNull(payment, HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 4. 执行单笔退款
        UnifiedRefundRS rs = executeSingleRefund(payment, refundRQ.getAmount(), refundRQ);
        return Result.success(rs);
    }

    /**
     * 核心的、可复用的单笔退款执行逻辑
     *
     * @param payment      要退款的支付单
     * @param refundAmount 本次退款的具体金额
     * @param originalRQ   原始退款请求
     * @return 统一退款结果
     */
    private UnifiedRefundRS executeSingleRefund(PaymentDO payment, Integer refundAmount, RefundRQ originalRQ) {
        // 1. 获取支付通道配置
        PaywayEnum paywayEnum = PaywayEnum.explain(payment.getPayType());
        ValidateUtil.requiredNotNull(paywayEnum, HeraBizErrorCodeEnum.ROUTE_EMPTY);

        PayChannelConfigContext configContext = configContextService.getConfig(payment.getPayConfigId());
        ValidateUtil.requiredNotNull(configContext, HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);

        // 2. 获取退款服务实现
        IRefundService refundService = this.getRefundService(paywayEnum);

        // 3. 生成退款单
        String refundNo = refundService.genRefundNo();
        RefundDO refundDO = this.genRefund(originalRQ, payment, refundNo, refundAmount);

        // 4. 调用预检查接口
        RefundOrderRQ refundOrderRQ = new RefundOrderRQ();
        refundOrderRQ.setRefundAmount(refundAmount);
        refundOrderRQ.setReason(originalRQ.getRefundReason());
        refundOrderRQ.setRefundNo(refundNo);
        refundOrderRQ.setCurrency(payment.getCurrency());
        String errMsg = refundService.preCheck(refundOrderRQ, refundDO, payment);
        if (StringUtils.isNotEmpty(errMsg)) {
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT.getCode(), errMsg);
        }

        // 5. 调用渠道退款接口
        UnifiedRefundRS rs = refundService.refund(refundOrderRQ, refundDO, payment, configContext);

        // 6. 处理上游返回数据
        this.processChannelMsg(rs.getChannelRetMsg(), refundDO);

        // 7. 返回结果
        return rs;
    }

    /**
     * 查找用于订单退款的支付单
     */
    private PaymentDO findPaymentForOrderRefund(RefundRQ refundRQ) {
        PaymentDO payment = null;
        if (StringUtils.isNotEmpty(refundRQ.getPayTradeNo())) {
            payment = paymentRecordMapper.getPaymentByPayTradeNo(refundRQ.getPayTradeNo());
        }
        if (payment == null && StringUtils.isNotEmpty(refundRQ.getBizTradeNo())) {
            payment = paymentRecordMapper.getPaymentByBizTradeNo(refundRQ.getBizTradeNo());
        }
        return payment;
    }

    /**
     * 校验订单退款的可退金额
     */
    private void validateRefundableAmount(RefundRQ refundRQ, PaymentDO payment) {
        List<RefundDO> existedRefunds = refundMapper.selectByTradeNo(payment.getPayTradeNo());
        long totalRefundedAmount = existedRefunds.stream()
                .filter(r -> !RefundStatus.FAIL.getCode().equals(r.getStatus()))
                .mapToLong(RefundDO::getAmount)
                .sum();
        long remainingAmount = payment.getPayAmount() - totalRefundedAmount;
        if (refundRQ.getAmount() > remainingAmount) {
            throw new HeraBizException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT.getCode(), "退款金额超过订单可退金额");
        }
    }

    private IRefundService getRefundService(PaywayEnum paywayEnum) {
        return beanContextHelper.getBean(paywayEnum.getChannel().getChannelName() + REFUND_SERVICE_NAME,IRefundService.class);
    }

    private RefundDO genRefund(RefundRQ refundRQ, PaymentDO payment, String refundNo, Integer amount) {
        RefundDO refundDO = new RefundDO();
        refundDO.setTradeNo(payment.getPayTradeNo());
        refundDO.setOrderNo(payment.getBizTradeNo());
        refundDO.setRefundNo(refundNo);
        refundDO.setAmount(amount);
        refundDO.setStatus(RefundStatus.INIT.getCode());
        refundDO.setSystem(refundRQ.getSystem());
        refundDO.setCurrency(payment.getCurrency());
        refundDO.setBizType(payment.getBizType());
        refundDO.setRefundType(payment.getPayType());
        refundDO.setReason(refundRQ.getRefundReason());
        return refundDO;
    }

    private void processChannelMsg(ChannelRetMsg channelRetMsg, RefundDO refundDO) {
        // refundPersistService.update(refundDO);
        // TODO 保存渠道返回信息，更新退款单状态等
    }
}
