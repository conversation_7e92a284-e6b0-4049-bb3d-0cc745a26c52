package so.dian.hera.service.v2.model;

import com.chargebolt.hera.client.dto.pay.refund.v2.RefundRQ;
import com.chargebolt.hera.domain.PaymentDO;
import com.chargebolt.hera.domain.RentalAuthRecordDO;
import lombok.Data;

/**
 * 退款上下文
 * 用于在退款流程中传递相关信息
 * 
 * <AUTHOR>
 */
@Data
public class PaymentRefundContext {
    
    /** 目标支付单（用户请求退款的支付单） */
    private PaymentDO targetPayment;
    
    /** 退款金额 */
    private Integer refundAmount;
    
    /** 原始退款请求 */
    private RefundRQ originalRequest;
    
    /** 退款来源类型 */
    private RefundSourceType refundType;
    
    /** 实际用于退款的支付单（可能与目标支付单不同） */
    private PaymentDO actualRefundPayment;
    
    /** 实际退款的第三方单号（用于预授权场景） */
    private String actualRefundChannelNo;
    
    /** 关联的授权凭证记录 */
    private RentalAuthRecordDO authRecord;
}
